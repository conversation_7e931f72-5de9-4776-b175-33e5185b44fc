<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Santri;
use App\Models\Komplek;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class SantriController extends Controller
{
    /**
     * Get all santri for a specific komplek
     */
    public function index($komplek_id, Request $request)
    {
        try {
            // Validate that komplek exists
            if (!is_numeric($komplek_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID komplek harus berupa angka.',
                ], 400);
            }

            // Check if komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $query = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan'])
                ->where('id_komplek', $komplek_id);

            // Optional filters
            if ($request->has('status') && $request->status) {
                switch ($request->status) {
                    case 'aktif':
                        $query->where('is_aktif', true);
                        break;
                    case 'nonaktif':
                        $query->where('is_nonaktif', true);
                        break;
                    case 'alumni':
                        $query->where('is_alumni', true);
                        break;
                    case 'diterima':
                        $query->where('diterima', true);
                        break;
                }
            }

            // Search by name
            if ($request->has('search') && $request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('nama', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk_baru', 'like', '%' . $request->search . '%');
                });
            }

            // Filter by gender
            if ($request->has('jenis_kelamin') && $request->jenis_kelamin) {
                $query->where('jenis_kelamin', $request->jenis_kelamin);
            }

            // Filter by year
            if ($request->has('tahun_masuk') && $request->tahun_masuk) {
                $query->where('tahun_masuk', $request->tahun_masuk);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $santri = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Data santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get all santri (for admin/super admin)
     */
    public function all(Request $request)
    {
        try {
            $query = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan']);

            // Optional filters
            if ($request->has('id_komplek') && $request->id_komplek) {
                $query->where('id_komplek', $request->id_komplek);
            }

            if ($request->has('status') && $request->status) {
                switch ($request->status) {
                    case 'aktif':
                        $query->where('is_aktif', true);
                        break;
                    case 'nonaktif':
                        $query->where('is_nonaktif', true);
                        break;
                    case 'alumni':
                        $query->where('is_alumni', true);
                        break;
                    case 'diterima':
                        $query->where('diterima', true);
                        break;
                }
            }

            // Search by name
            if ($request->has('search') && $request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('nama', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk_baru', 'like', '%' . $request->search . '%');
                });
            }

            // Filter by gender
            if ($request->has('jenis_kelamin') && $request->jenis_kelamin) {
                $query->where('jenis_kelamin', $request->jenis_kelamin);
            }

            // Filter by year
            if ($request->has('tahun_masuk') && $request->tahun_masuk) {
                $query->where('tahun_masuk', $request->tahun_masuk);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $santri = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Data santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get specific santri by ID
     */
    public function show($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find santri that belongs to the specified komplek
            $santri = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan', 'admin'])
                ->where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            return response()->json([
                'success' => true,
                'message' => 'Detail santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Santri tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get santri statistics for a komplek
     */
    public function statistics($komplek_id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $stats = [
                'total' => Santri::where('id_komplek', $komplek_id)->count(),
                'aktif' => Santri::where('id_komplek', $komplek_id)->where('is_aktif', true)->count(),
                'nonaktif' => Santri::where('id_komplek', $komplek_id)->where('is_nonaktif', true)->count(),
                'alumni' => Santri::where('id_komplek', $komplek_id)->where('is_alumni', true)->count(),
                'diterima' => Santri::where('id_komplek', $komplek_id)->where('diterima', true)->count(),
                'laki_laki' => Santri::where('id_komplek', $komplek_id)->where('jenis_kelamin', 'laki-laki')->count(),
                'perempuan' => Santri::where('id_komplek', $komplek_id)->where('jenis_kelamin', 'perempuan')->count(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Statistik santri berhasil diambil.',
                'data' => $stats,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil statistik santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Create new santri
     */
    public function store(Request $request, $komplek_id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $validated = $request->validate([
                'nomor_induk' => ['nullable', 'string', 'max:50'],
                'nomor_induk_baru' => ['nullable', 'string', 'max:50'],
                'nama' => ['required', 'string', 'max:255'],
                'tanggal_lahir' => ['required', 'date'],
                'tempat_lahir' => ['nullable', 'string', 'max:255'],
                'jenis_kelamin' => ['required', 'in:laki-laki,perempuan'],
                'hp' => ['nullable', 'string', 'max:20'],
                'email' => ['nullable', 'email', 'max:255'],
                'alamat' => ['nullable', 'string'],
                'city_code' => ['nullable', 'string', 'max:4'],
                'district_code' => ['nullable', 'string', 'max:7'],
                'nik' => ['nullable', 'string', 'max:20'],
                'kk' => ['nullable', 'string', 'max:20'],
                'nama_ayah' => ['nullable', 'string', 'max:255'],
                'nama_ibu' => ['nullable', 'string', 'max:255'],
                'pekerjaan_ayah' => ['nullable', 'string', 'max:255'],
                'pekerjaan_ibu' => ['nullable', 'string', 'max:255'],
                'hp_orang_tua' => ['nullable', 'string', 'max:20'],
                'alamat_orang_tua' => ['nullable', 'string'],
                'anak_ke' => ['nullable', 'integer', 'min:1'],
                'jumlah_saudara' => ['nullable', 'integer', 'min:0'],
                'pendidikan_terakhir' => ['nullable', 'string', 'max:100'],
                'tahun_masuk' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
                'id_kamar' => ['nullable', 'exists:kamars,id'],
            ]);

            // Add komplek_id and admin_id from route parameter and authenticated user
            $validated['id_komplek'] = $komplek_id;
            $validated['id_admin'] = $request->user()->id;

            // Set default values
            $validated['diterima'] = true;
            $validated['diterima_at'] = now();
            $validated['is_aktif'] = true;
            $validated['aktif_at'] = now();
            $validated['is_nonaktif'] = false;
            $validated['is_alumni'] = false;

            $santri = Santri::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Santri berhasil ditambahkan.',
                'data' => $santri->load(['komplek', 'kamar', 'kabupaten', 'kecamatan']),
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Update santri
     */
    public function update(Request $request, $komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find santri that belongs to the specified komplek
            $santri = Santri::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            $validated = $request->validate([
                'nomor_induk' => ['nullable', 'string', 'max:50'],
                'nomor_induk_baru' => ['nullable', 'string', 'max:50'],
                'nama' => ['required', 'string', 'max:255'],
                'tanggal_lahir' => ['required', 'date'],
                'tempat_lahir' => ['nullable', 'string', 'max:255'],
                'jenis_kelamin' => ['required', 'in:laki-laki,perempuan'],
                'hp' => ['nullable', 'string', 'max:20'],
                'email' => ['nullable', 'email', 'max:255'],
                'alamat' => ['nullable', 'string'],
                'city_code' => ['nullable', 'string', 'max:4'],
                'district_code' => ['nullable', 'string', 'max:7'],
                'nik' => ['nullable', 'string', 'max:20'],
                'kk' => ['nullable', 'string', 'max:20'],
                'nama_ayah' => ['nullable', 'string', 'max:255'],
                'nama_ibu' => ['nullable', 'string', 'max:255'],
                'pekerjaan_ayah' => ['nullable', 'string', 'max:255'],
                'pekerjaan_ibu' => ['nullable', 'string', 'max:255'],
                'hp_orang_tua' => ['nullable', 'string', 'max:20'],
                'alamat_orang_tua' => ['nullable', 'string'],
                'anak_ke' => ['nullable', 'integer', 'min:1'],
                'jumlah_saudara' => ['nullable', 'integer', 'min:0'],
                'pendidikan_terakhir' => ['nullable', 'string', 'max:100'],
                'tahun_masuk' => ['required', 'integer', 'min:1900', 'max:' . (date('Y') + 1)],
                'id_kamar' => ['nullable', 'exists:kamars,id'],
            ]);

            $santri->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Santri berhasil diperbarui.',
                'data' => $santri->load(['komplek', 'kamar', 'kabupaten', 'kecamatan']),
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Santri tidak ditemukan.',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Delete santri
     */
    public function destroy($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find santri that belongs to the specified komplek
            $santri = Santri::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            $santri->delete();

            return response()->json([
                'success' => true,
                'message' => 'Santri berhasil dihapus.',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Santri tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Update santri status (aktif, nonaktif, alumni)
     */
    public function updateStatus(Request $request, $komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find santri that belongs to the specified komplek
            $santri = Santri::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            $validated = $request->validate([
                'status' => ['required', 'in:aktif,nonaktif,alumni'],
                'alasan' => ['nullable', 'string'],
            ]);

            // Reset all status flags
            $santri->is_aktif = false;
            $santri->aktif_at = null;
            $santri->is_nonaktif = false;
            $santri->nonaktif_at = null;
            $santri->is_alumni = false;
            $santri->alumni_at = null;

            // Set the appropriate status
            switch ($validated['status']) {
                case 'aktif':
                    $santri->is_aktif = true;
                    $santri->aktif_at = now();
                    break;
                case 'nonaktif':
                    $santri->is_nonaktif = true;
                    $santri->nonaktif_at = now();
                    break;
                case 'alumni':
                    $santri->is_alumni = true;
                    $santri->alumni_at = now();
                    break;
            }

            if (isset($validated['alasan'])) {
                $santri->alasan = $validated['alasan'];
            }

            $santri->save();

            return response()->json([
                'success' => true,
                'message' => 'Status santri berhasil diperbarui.',
                'data' => $santri->load(['komplek', 'kamar', 'kabupaten', 'kecamatan']),
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Santri tidak ditemukan.',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui status santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
